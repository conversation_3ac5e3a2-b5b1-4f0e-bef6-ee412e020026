diff --git a/ios/Pods/Headers/Private/fmt/fmt/core.h b/ios/Pods/Headers/Private/fmt/fmt/core.h
index 67f41a2..7d2d91e 100644
--- a/ios/Pods/Headers/Private/fmt/fmt/core.h
+++ b/ios/Pods/Headers/Private/fmt/fmt/core.h
@@ -377,7 +377,9 @@ using wstring_view = basic_string_view<wchar_t>;
 template <typename Char>
 struct formatter<basic_string_view<Char>, Char>
     : formatter<basic_string_view<Char>, Char> {};

+#if FMT_USE_CHAR8_T
 using char8_type = char8_t;
 template <>
 struct formatter<basic_string_view<char8_type>, char8_type>
     : formatter<string_view, char8_type> {};
+#endif
 }  // namespace internal
 }  // namespace fmt

 

 @@ -377,7 +377,9 @@ using wstring_view = basic_string_view<wchar_t>;
 template <typename Char>
 struct formatter<basic_string_view<Char>, Char>
     : formatter<basic_string_view<Char>, Char> {};

+#if FMT_USE_CHAR8_T
 using char8_type = char8_t;
 template <>
 struct formatter<basic_string_view<char8_type>, char8_type>
     : formatter<string_view, char8_type> {};
+#endif

