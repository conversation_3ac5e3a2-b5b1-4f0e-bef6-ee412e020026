# diff --git a/ios/Pods/Headers/Private/fmt/fmt/core.h b/ios/Pods/Headers/Private/fmt/fmt/core.h
# index 1234567..abcdefg 100644
# --- a/ios/Pods/Headers/Private/fmt/fmt/core.h
# +++ b/ios/Pods/Headers/Private/fmt/fmt/core.h
# @@ -334,9 +334,6 @@ template <typename Char> constexpr bool is_unicode() {

#  #ifdef __cpp_char8_t
# -using char8_type = char8_t;
# -#else
# -enum char8_type : unsigned char {};
# +// Disable char8_t support for Xcode 16.3+ compatibility - char8_type removed
#  #endif
#  }  // namespace internal

# @@ -440,10 +437,6 @@ using string_view = basic_string_view<char>;
#  using wstring_view = basic_string_view<wchar_t>;

# -#ifndef __cpp_char8_t
# -// char8_t is deprecated; use char instead.
# -using char8_t FMT_DEPRECATED_ALIAS = internal::char8_type;
# -#endif
# -
#  /** Specifies if ``T`` is a character type. Can be specialized by users. */
#  template <typename T> struct is_char : std::false_type {};
#  template <> struct is_char<char> : std::true_type {};
#  template <> struct is_char<wchar_t> : std::true_type {};
# -template <> struct is_char<internal::char8_type> : std::true_type {};
#  template <> struct is_char<char16_t> : std::true_type {};
#  template <> struct is_char<char32_t> : std::true_type {};

# diff --git a/ios/Pods/Headers/Private/fmt/fmt/format.h b/ios/Pods/Headers/Private/fmt/fmt/format.h
# index 2345678..bcdefgh 100644
# --- a/ios/Pods/Headers/Private/fmt/fmt/format.h
# +++ b/ios/Pods/Headers/Private/fmt/fmt/format.h
# @@ -574,12 +574,6 @@ template <typename Char> struct named_arg : named_arg_info<Char> {
#  };

# -class FMT_DEPRECATED u8string_view
# -    : public basic_string_view<internal::char8_type> {
# - public:
# -  u8string_view(const char* s)
# -      : basic_string_view<internal::char8_type>(
# -            reinterpret_cast<const internal::char8_type*>(s)) {}
# -  u8string_view(const char* s, size_t count) FMT_NOEXCEPT
# -      : basic_string_view<internal::char8_type>(
# -            reinterpret_cast<const internal::char8_type*>(s), count) {}
# -};
# +// u8string_view removed for Xcode 16.3+ compatibility

#  // A formatter specialization for the core types corresponding to the built-in
#  // types supporting argument conversion to printf with consistent behavior.
