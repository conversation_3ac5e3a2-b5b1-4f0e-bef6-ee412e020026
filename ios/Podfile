require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '14.0'

target 'ReactNativeBridgeIos' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    :hermes_enabled => false,
    :flipper_configuration => FlipperConfiguration.disabled
  )

  target 'ReactNativeBridgeIosTests' do
    inherit! :complete
  end

  post_install do |installer|
    react_native_post_install(installer)

    # Global C++ flags to avoid char8_t errors
    installer.pods_project.build_configurations.each do |config|
      flags = config.build_settings['OTHER_CPLUSPLUSFLAGS']
      flags = [flags] if flags.is_a?(String)
      flags ||= []
      flags += ['-DFMT_USE_CHAR8_T=0', '-DFMT_DISABLE_CHAR8_T=1']
      config.build_settings['OTHER_CPLUSPLUSFLAGS'] = flags.uniq
    end

    # Patch fmt/core.h to guard char8_type definition
    fmt_header_path = File.join(__dir__, 'Pods/Headers/Private/fmt/fmt/core.h')
    if File.exist?(fmt_header_path)
      content = File.read(fmt_header_path)
      patched_content = content.gsub(
        /^(\s*)using char8_type = char;/,
        "\\1#ifndef FMT_USE_CHAR8_T\n\\1using char8_type = char;\n\\1#endif"
      )

      if content != patched_content
        puts "🔧 Patching fmt/core.h to disable char8_t..."
        File.write(fmt_header_path, patched_content)
      else
        puts "✅ fmt/core.h already patched."
      end
    else
      puts "⚠️ fmt/core.h not found — skipping patch."
    end

    # Target-specific settings
    installer.generated_projects.each do |project|
      project.targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings['DEVELOPMENT_TEAM'] = 'WF47643S5X'

          # Base compatibility definitions
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] += [
            '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION'
          ]

          # Add char8_t suppression to known C++ targets
          if ['Yoga', 'React-Core', 'React-cxxreact', 'React-jsi', 'React-jsiexecutor', 'ReactCommon'].include?(target.name)
            flags = config.build_settings['OTHER_CPLUSPLUSFLAGS']
            flags = [flags] if flags.is_a?(String)
            flags ||= []
            flags += ['-Wno-builtin-macro-redefined']
            config.build_settings['OTHER_CPLUSPLUSFLAGS'] = flags.uniq
          end
        end
      end
    end
  end
end
