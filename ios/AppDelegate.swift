//
//  AppDelegate.swift
//  ReactNativeBridgeIos
//
//  Created by PATHOMPHONG CHAROENWICHIANCHAY on 15/10/21.
//

import UIKit
import Foundation
import EkycFramework

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, URLSessionDelegate {
  var window: UIWindow?
  var bridge: RCTBridge!
  var fromViewController: UIViewController?
  var nonProdToken: String = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  
  var customizeTheme: CustomizeTheme = CustomizeTheme(text: nil,
                                                      button: nil,
                                                      oval: nil,
                                                      ocr: nil,
                                                      image: Image(logo: UIImage(named: "TechX_Logo"),
                                                                   closeImage: nil,
                                                                   idCardImage: nil,
                                                                   permissionCameraImage: nil,
                                                                   activeFlash: nil,
                                                                   inactiveFlash: nil),
                                                      border: nil,
                                                      ndid: nil,
                                                      other: nil)
  
  private func configToken() -> String {
    switch BuildConfiguration.shared.environment {
    default:
      return nonProdToken
    }
  }
  
  func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    let jsCodeLocation = RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index", fallbackExtension: nil)!
    let rootView = RCTRootView(bundleURL: jsCodeLocation, moduleName: "ReactNativeBridgeIos", initialProperties: nil, launchOptions: launchOptions)
    
    self.window = UIWindow(frame: UIScreen.main.bounds)
    let reactNativeViewController = UIViewController()
    reactNativeViewController.view = rootView
    let reactNavigationController = UINavigationController(rootViewController: reactNativeViewController)
    reactNavigationController.isNavigationBarHidden = true
    reactNavigationController.modalPresentationStyle = .fullScreen
    self.window?.rootViewController = reactNavigationController
    self.window?.makeKeyAndVisible()
    self.fromViewController = reactNavigationController
    return true
  }
  
  func goOcrIdCardVerifyByFaceStoryboard(token: String, checkDopa: Bool, checkExpiredIdCard: Bool, enableConfirmInfo: Bool, ocrPrefill: OcrPrefill) {
    // Your app id must be com.scb.techx.*,com.techx.ekyc.*,com.scbs.easyinvest.*
    var userToken: String = configToken()
    if !token.isEmpty {
      userToken = token
    }
    EkycFrameworkKit.initEkyc(sessionId: NSUUID().uuidString.lowercased(),
                              token: userToken,
                              environment: BuildConfiguration.shared.environment.rawValue,
                              customizeTheme: customizeTheme,
                              language: nil) { success, description, ekycToken  in
      guard let rootVc = self.fromViewController else {
        return
      }
      DispatchQueue.main.async {
        self.goToOcrIdCardVerifyByFace(rootVc: rootVc,
                                       checkDopa: checkDopa,
                                       checkExpiredIdCard: checkExpiredIdCard,
                                       enableConfirmInfo: enableConfirmInfo,
                                       ocrPrefill: ocrPrefill,
                                       token: ekycToken)
      }
    }
  }
  
  func goOcrIdCardStoryboard(token: String, checkDopa: Bool, checkExpiredIdCard: Bool, enableConfirmInfo: Bool, ocrPrefill: OcrPrefill) {
    // Your app id must be com.scb.techx.*,com.techx.ekyc.*,com.scbs.easyinvest.*
    var userToken: String = configToken()
    if !token.isEmpty {
      userToken = token
    }
    EkycFrameworkKit.initEkyc(sessionId: NSUUID().uuidString.lowercased(),
                              token: userToken,
                              environment: BuildConfiguration.shared.environment.rawValue,
                              customizeTheme: customizeTheme,
                              language: nil) { success, description, ekycToken  in
      guard let rootVc = self.fromViewController else {
        return
      }
      DispatchQueue.main.async {
        self.goToOcrIdCard(rootVc: rootVc,
                                       checkDopa: checkDopa,
                                       checkExpiredIdCard: checkExpiredIdCard,
                                       enableConfirmInfo: enableConfirmInfo,
                                       ocrPrefill: ocrPrefill,
                                       token: ekycToken)
      }
    }
  }
  
  func goLivenessStoryboard(token: String) {
    // Your app id must be com.scb.techx.*,com.techx.ekyc.*,com.scbs.easyinvest.*
    var userToken: String = configToken()
    if !token.isEmpty {
      userToken = token
    }
    EkycFrameworkKit.initEkyc(sessionId: NSUUID().uuidString.lowercased(),
                              token: userToken,
                              environment: BuildConfiguration.shared.environment.rawValue,
                              customizeTheme: customizeTheme,
                              language: nil) { success, description, ekycToken  in
      guard let rootVc = self.fromViewController else {
        return
      }
      DispatchQueue.main.async {
        self.goToLivenessCheck(rootVc: rootVc, token: ekycToken)
      }
    }
  }
  
  func goNdidStoryboard(token: String, cidNumber: String?) {
    // Your app id must be com.scb.techx.*,com.techx.ekyc.*,com.scbs.easyinvest.*
    var userToken: String = configToken()
    if !token.isEmpty {
      userToken = token
    }
    EkycFrameworkKit.initEkyc(sessionId: NSUUID().uuidString.lowercased(),
                              token: userToken,
                              environment: BuildConfiguration.shared.environment.rawValue,
                              customizeTheme: nil,
                              language: nil) { success, description, ekycToken  in
      guard let rootVc = self.fromViewController else {
        return
      }
      DispatchQueue.main.async {
        self.goToNdidVerification(rootVc: rootVc, cidNumber: cidNumber, token: ekycToken)
      }
    }
  }
  
  private func goToNdidVerification(rootVc: UIViewController, cidNumber: String?, token: String?) {
    var idNumber: String = "1309913659936"
    if let cid = cidNumber, !cid.isEmpty {
      idNumber = cid
    }
    EkycFrameworkKit.ndidVerification(fromViewController: rootVc,
                                      identifierType: "national_id",
                                      identifierValue: idNumber,
                                      serviceId: "001.cust_info_001",
                                      callBack: { success, description, ndidStatus, ndidError, ndidData in
      let status = ndidStatus ?? "(null)"
      let error = ndidError?.code ?? "(null)"
      let ekycToken = token ?? "(null)"
      let referenceId = ndidData?.referenceId ?? "(null)"
      let requestId = ndidData?.requestId ?? "(null)"
      let data = "success: " + String(success)
      + "\ndescription: " + description
      + "\nndidStatus: " + status
      + "\nndidError: " + error
      + "\nndidReferenceId: " + referenceId
      + "\nndidRequestId: " + requestId
      + "\nekycToken: " + ekycToken
      AppProperties().setUserConfirmValue(key: data)
    })
  }
  
  private func goToOcrIdCardVerifyByFace(rootVc: UIViewController, checkDopa: Bool, checkExpiredIdCard: Bool, enableConfirmInfo: Bool, ocrPrefill: OcrPrefill, token: String?) {
    EkycFrameworkKit.ocrIdCardVerifyByFace(fromViewController: rootVc,
                                           checkExpiredIdCard: checkExpiredIdCard,
                                           checkDopa: checkDopa,
                                           enableConfirmInfo: enableConfirmInfo,
                                           ocrPrefill: ocrPrefill) { success, description, userOcrValue, userConfirmedValue, dopaResult in
      let ekycToken = token ?? "(null)"
      let code = dopaResult?.code ?? "(null)"
      let desc = dopaResult?.desc ?? "(null)"
      var userOcr = "(null)"
      if let ocrValue = userOcrValue {
        let nationalId = ocrValue.nationalId
        let titleTh = ocrValue.titleTh
        let firstNameTh = ocrValue.firstNameTh
        let middleNameTh = ocrValue.middleNameTh
        let lastNameTh = ocrValue.lastNameTh
        let titleEn = ocrValue.titleEn
        let firstNameEn = ocrValue.firstNameEn
        let middleNameEn = ocrValue.middleNameEn
        let lastNameEn = ocrValue.lastNameEn
        let dateOfBirth = ocrValue.dateOfBirth
        let dateOfIssue = ocrValue.dateOfIssue
        let dateOfExpiry = ocrValue.dateOfExpiry
        let laserId = ocrValue.laserId
        userOcr = "\n\tnationalId: " + nationalId
        + "\n\ttitleTh: " + titleTh
        + "\n\tfirstNameTh: " + firstNameTh
        + "\n\tmiddleNameTh: " + middleNameTh
        + "\n\tlastNameTh: " + lastNameTh
        + "\n\ttitleEn: " + titleEn
        + "\n\tfirstNameEn: " + firstNameEn
        + "\n\tmiddleNameEn: " + middleNameEn
        + "\n\tlastNameEn: " + lastNameEn
        + "\n\tdateOfBirth: " + dateOfBirth
        + "\n\tdateOfIssue: " + dateOfIssue
        + "\n\tdateOfExpiry: " + dateOfExpiry
        + "\n\tlaserId: " + laserId
      }
      var userConfirmed = "(null)"
      if let confirmValue = userConfirmedValue {
        let nationalId = confirmValue.nationalId
        let titleTh = confirmValue.titleTh
        let firstNameTh = confirmValue.firstNameTh
        let middleNameTh = confirmValue.middleNameTh
        let lastNameTh = confirmValue.lastNameTh
        let titleEn = confirmValue.titleEn
        let firstNameEn = confirmValue.firstNameEn
        let middleNameEn = confirmValue.middleNameEn
        let lastNameEn = confirmValue.lastNameEn
        let dateOfBirth = confirmValue.dateOfBirth
        let dateOfIssue = confirmValue.dateOfIssue
        let dateOfExpiry = confirmValue.dateOfExpiry
        let laserId = confirmValue.laserId
        userConfirmed = "\n\tnationalId: " + nationalId
        + "\n\ttitleTh: " + titleTh
        + "\n\tfirstNameTh: " + firstNameTh
        + "\n\tmiddleNameTh: " + middleNameTh
        + "\n\tlastNameTh: " + lastNameTh
        + "\n\ttitleEn: " + titleEn
        + "\n\tfirstNameEn: " + firstNameEn
        + "\n\tmiddleNameEn: " + middleNameEn
        + "\n\tlastNameEn: " + lastNameEn
        + "\n\tdateOfBirth: " + dateOfBirth
        + "\n\tdateOfIssue: " + dateOfIssue
        + "\n\tdateOfExpiry: " + dateOfExpiry
        + "\n\tlaserId: " + laserId
      }
      let installationIdString = "\nInstallationId: " + (EkycFrameworkKit.getInstallationId() ?? "(null)")
      let data = "success: " + String(success)
      + "\ndescription: " + description
      + "\nuserOcrValue: " + userOcr
      + "\nuserConfirmedValue: " + userConfirmed
      + "\ndopaCode: " + code
      + "\ndopaDescription: " + desc
      + "\nekycToken: " + ekycToken
      + installationIdString
      + "\n"
      AppProperties().setUserConfirmValue(key: data)
    }
  }
  
  private func goToOcrIdCard(rootVc: UIViewController, checkDopa: Bool, checkExpiredIdCard: Bool, enableConfirmInfo: Bool, ocrPrefill: OcrPrefill, token: String?) {
    EkycFrameworkKit.ocrIdCard(fromViewController: rootVc,
                                           checkExpiredIdCard: checkExpiredIdCard,
                                           checkDopa: checkDopa,
                                           enableConfirmInfo: enableConfirmInfo,
                                           ocrPrefill: ocrPrefill) { success, description, userOcrValue, userConfirmedValue, dopaResult in
      let ekycToken = token ?? "(null)"
      let code = dopaResult?.code ?? "(null)"
      let desc = dopaResult?.desc ?? "(null)"
      var userOcr = "(null)"
      if let ocrValue = userOcrValue {
        let nationalId = ocrValue.nationalId
        let titleTh = ocrValue.titleTh
        let firstNameTh = ocrValue.firstNameTh
        let middleNameTh = ocrValue.middleNameTh
        let lastNameTh = ocrValue.lastNameTh
        let titleEn = ocrValue.titleEn
        let firstNameEn = ocrValue.firstNameEn
        let middleNameEn = ocrValue.middleNameEn
        let lastNameEn = ocrValue.lastNameEn
        let dateOfBirth = ocrValue.dateOfBirth
        let dateOfIssue = ocrValue.dateOfIssue
        let dateOfExpiry = ocrValue.dateOfExpiry
        let laserId = ocrValue.laserId
        userOcr = "\n\tnationalId: " + nationalId
        + "\n\ttitleTh: " + titleTh
        + "\n\tfirstNameTh: " + firstNameTh
        + "\n\tmiddleNameTh: " + middleNameTh
        + "\n\tlastNameTh: " + lastNameTh
        + "\n\ttitleEn: " + titleEn
        + "\n\tfirstNameEn: " + firstNameEn
        + "\n\tmiddleNameEn: " + middleNameEn
        + "\n\tlastNameEn: " + lastNameEn
        + "\n\tdateOfBirth: " + dateOfBirth
        + "\n\tdateOfIssue: " + dateOfIssue
        + "\n\tdateOfExpiry: " + dateOfExpiry
        + "\n\tlaserId: " + laserId
      }
      var userConfirmed = "(null)"
      if let confirmValue = userConfirmedValue {
        let nationalId = confirmValue.nationalId
        let titleTh = confirmValue.titleTh
        let firstNameTh = confirmValue.firstNameTh
        let middleNameTh = confirmValue.middleNameTh
        let lastNameTh = confirmValue.lastNameTh
        let titleEn = confirmValue.titleEn
        let firstNameEn = confirmValue.firstNameEn
        let middleNameEn = confirmValue.middleNameEn
        let lastNameEn = confirmValue.lastNameEn
        let dateOfBirth = confirmValue.dateOfBirth
        let dateOfIssue = confirmValue.dateOfIssue
        let dateOfExpiry = confirmValue.dateOfExpiry
        let laserId = confirmValue.laserId
        userConfirmed = "\n\tnationalId: " + nationalId
        + "\n\ttitleTh: " + titleTh
        + "\n\tfirstNameTh: " + firstNameTh
        + "\n\tmiddleNameTh: " + middleNameTh
        + "\n\tlastNameTh: " + lastNameTh
        + "\n\ttitleEn: " + titleEn
        + "\n\tfirstNameEn: " + firstNameEn
        + "\n\tmiddleNameEn: " + middleNameEn
        + "\n\tlastNameEn: " + lastNameEn
        + "\n\tdateOfBirth: " + dateOfBirth
        + "\n\tdateOfIssue: " + dateOfIssue
        + "\n\tdateOfExpiry: " + dateOfExpiry
        + "\n\tlaserId: " + laserId
      }
      let installationIdString = "\nInstallationId: " + (EkycFrameworkKit.getInstallationId() ?? "(null)")
      let data = "success: " + String(success)
      + "\ndescription: " + description
      + "\nuserOcrValue: " + userOcr
      + "\nuserConfirmedValue: " + userConfirmed
      + "\ndopaCode: " + code
      + "\ndopaDescription: " + desc
      + "\nekycToken: " + ekycToken
      + installationIdString
      + "\n"
      AppProperties().setUserConfirmValue(key: data)
    }
  }
  
  private func goToLivenessCheck(rootVc: UIViewController, token: String?) {
    let ekycToken = token ?? "(null)"
    EkycFrameworkKit.livenessCheck(fromViewController: rootVc) { success, description in
      let installationIdString = "\nInstallationId: " + (EkycFrameworkKit.getInstallationId() ?? "(null)")
      let data = "success: " + String(success)
      + "\ndescription: " + description
      + "\nekycToken: " + ekycToken
      + installationIdString
      + "\n"
      AppProperties().setUserConfirmValue(key: data)
    }
  }
}

class AppProperties {
  private var myDefault:UserDefaults?
  
  init() {
    myDefault = UserDefaults.standard
  }
  
  func setUserConfirmValue(key: String) {
    myDefault?.set("\(key)", forKey: UserDefaultConstant.USERCONFIRMEDVALUE)
  }
  
  func getUserConfirmValue() -> (String) {
    guard let value = myDefault?.object(forKey: UserDefaultConstant.USERCONFIRMEDVALUE) as? String else {
      return ""
    }
    return value
  }
  
}

struct UserDefaultConstant {
  static let USERCONFIRMEDVALUE = "USERCONFIRMEDVALUE"
}

extension AppDelegate {
  enum Environment: String {
    case dev = "DEV"
    case sit = "SIT"
    case uat = "UAT"
    case prod = "PROD"
  }
  
  class BuildConfiguration {
    static let shared = BuildConfiguration()
    
    var environment: Environment
    
    init() {
      let currentConfiguration = Bundle.main.object(forInfoDictionaryKey: "ENVIRONMENT") as! String
      environment = Environment(rawValue: currentConfiguration)!
    }
  }
}
